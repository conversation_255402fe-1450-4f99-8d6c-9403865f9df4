-- Name: f_dm_foc_month_cost_idx_dms_ict_dimension; Type: Function; Schema: fin_dm_opt_foi;
-- 专用于ICT产业量纲颗粒度的月度指数计算函数
-- 抽离自f_dm_foc_month_cost_idx_dms，固化参数：F_INDUSTRY_FLAG='I', F_DIMENSION_TYPE='D', F_ITEM_VERSION=NULL

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_month_cost_idx_dms_ict_dimension(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2024年12月
创建人  ：系统重构
功能描述：ICT产业量纲颗粒度月度分析指数表数据初始化专用函数
背景描述：从原f_dm_foc_month_cost_idx_dms函数中抽离出专门处理ICT产业量纲颗粒度的逻辑
固化参数：F_INDUSTRY_FLAG = 'I' (ICT产业)
         F_DIMENSION_TYPE = 'D' (量纲颗粒度)  
         F_ITEM_VERSION = NULL (自动获取最新版本)
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX_DMS_ICT_DIMENSION();
****************************************************************************************************************************************************************/

DECLARE
  -- ========== 基础控制变量定义 ==========
  V_SP_NAME                              VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX_DMS_ICT_DIMENSION'; -- 存储过程名称，用于日志记录
  V_VERSION                              BIGINT;      -- 数据版本号，从版本信息表自动获取最新版本
  V_STEP_NUM                             BIGINT := 0; -- 函数执行步骤计数器，用于日志追踪
  V_BASE_PERIOD_ID                       INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); -- 基期ID，格式为上一年01月
  V_SQL                                  TEXT;        -- 动态SQL语句变量
  V_LEVEL_NUM                            NUMERIC := 12; -- 量纲颗粒度固定12层循环：ITEM->CATEGORY->MODL->CEG->DIMENSION->SUBCATEGORY->SUB_DETAIL->SPART->LV3->LV2->LV1->LV0

  -- ========== ICT产业量纲颗粒度专用表名（固化配置）==========
  V_MID_TABLE                            VARCHAR(200) := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS';    -- 中间计算表，存储各层级卷积过程数据
  V_WEIGHT_TABLE                         VARCHAR(200) := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T';         -- 权重配置表，定义各层级间的权重关系
  V_TARGET_TABLE                         VARCHAR(200) := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T';       -- 最终结果表，存储计算完成的指数数据
  
  -- ========== 字段名称变量（用于动态SQL构建）==========
  -- 重量级团队层级字段（产品研发团队组织架构）
  V_PROD_RND_TEAM_CODE                   TEXT := 'PROD_RND_TEAM_CODE,';           -- 重量级团队编码字段
  V_PROD_RND_TEAM_CN_NAME                TEXT := 'PROD_RND_TEAM_CN_NAME,';       -- 重量级团队中文名称字段
  V_LV0_PROD_RND_TEAM_CODE               TEXT := 'LV0_PROD_RND_TEAM_CODE,';       -- LV0层级团队编码（最高层级）
  V_LV0_PROD_RD_TEAM_CN_NAME             TEXT := 'LV0_PROD_RD_TEAM_CN_NAME,';     -- LV0层级团队中文名称
  V_LV1_PROD_RND_TEAM_CODE               TEXT := 'LV1_PROD_RND_TEAM_CODE,';       -- LV1层级团队编码
  V_LV1_PROD_RD_TEAM_CN_NAME             TEXT := 'LV1_PROD_RD_TEAM_CN_NAME,';     -- LV1层级团队中文名称
  V_LV2_PROD_RND_TEAM_CODE               TEXT := 'LV2_PROD_RND_TEAM_CODE,';       -- LV2层级团队编码
  V_LV2_PROD_RD_TEAM_CN_NAME             TEXT := 'LV2_PROD_RD_TEAM_CN_NAME,';     -- LV2层级团队中文名称
  V_LV3_PROD_RND_TEAM_CODE               TEXT := 'LV3_PROD_RND_TEAM_CODE,';       -- LV3层级团队编码
  V_LV3_PROD_RD_TEAM_CN_NAME             TEXT := 'LV3_PROD_RD_TEAM_CN_NAME,';     -- LV3层级团队中文名称

  -- 专家团和模块层级字段
  V_TOP_L3_CEG_CODE                      TEXT := 'TOP_L3_CEG_CODE,';              -- L3专家团编码字段
  V_TOP_L3_CEG_SHORT_CN_NAME             TEXT := 'TOP_L3_CEG_SHORT_CN_NAME,';     -- L3专家团简称字段
  V_TOP_L4_CEG_CODE                      TEXT := 'TOP_L4_CEG_CODE,';              -- L4模块编码字段
  V_TOP_L4_CEG_SHORT_CN_NAME             TEXT := 'TOP_L4_CEG_SHORT_CN_NAME,';     -- L4模块简称字段

  -- ========== 量纲颗粒度相关字段（核心业务字段）==========
  V_DIMENSION_CODE                       TEXT := 'DIMENSION_CODE,';               -- 量纲编码（一级分类）
  V_DIMENSION_CN_NAME                    TEXT := 'DIMENSION_CN_NAME,';            -- 量纲中文名称
  V_DIMENSION_SUBCATEGORY_CODE           TEXT := 'DIMENSION_SUBCATEGORY_CODE,';   -- 量纲子类编码（二级分类）
  V_DIMENSION_SUBCATEGORY_CN_NAME        TEXT := 'DIMENSION_SUBCATEGORY_CN_NAME,'; -- 量纲子类中文名称
  V_DIMENSION_SUB_DETAIL_CODE            TEXT := 'DIMENSION_SUB_DETAIL_CODE,';    -- 量纲子类明细编码（三级分类）
  V_DIMENSION_SUB_DETAIL_CN_NAME         TEXT := 'DIMENSION_SUB_DETAIL_CN_NAME,'; -- 量纲子类明细中文名称
  V_DMS_CODE                             TEXT := 'DMS_CODE,';                     -- 量纲颗粒度编码（最细粒度）
  V_DMS_CN_NAME                          TEXT := 'DMS_CN_NAME,';                  -- 量纲颗粒度中文名称
  V_SPART_CODE                           TEXT := 'SPART_CODE,';                   -- SPART层级编码（四级分类）
  V_SPART_CN_NAME                        TEXT := 'SPART_CN_NAME,';                -- SPART层级中文名称
  
  -- SQL查询字段变量
  V_SQL_PROD_RND_TEAM_CODE               TEXT := 'T1.PROD_RND_TEAM_CODE,';
  V_SQL_PROD_RND_TEAM_CN_NAME            TEXT := 'T1.PROD_RND_TEAM_CN_NAME,';
  V_SQL_LV0_PROD_RND_TEAM_CODE           TEXT := 'T1.LV0_PROD_RND_TEAM_CODE,';
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME         TEXT := 'T1.LV0_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV1_PROD_RND_TEAM_CODE           TEXT := 'T1.LV1_PROD_RND_TEAM_CODE,';
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME         TEXT := 'T1.LV1_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV2_PROD_RND_TEAM_CODE           TEXT := 'T1.LV2_PROD_RND_TEAM_CODE,';
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME         TEXT := 'T1.LV2_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV3_PROD_RND_TEAM_CODE           TEXT := 'T1.LV3_PROD_RND_TEAM_CODE,';
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME         TEXT := 'T1.LV3_PROD_RD_TEAM_CN_NAME,';
  V_SQL_TOP_L3_CEG_CODE                  TEXT := 'T1.TOP_L3_CEG_CODE,';
  V_SQL_TOP_L3_CEG_SHORT_CN_NAME         TEXT := 'T1.TOP_L3_CEG_SHORT_CN_NAME,';
  V_SQL_TOP_L4_CEG_CODE                  TEXT := 'T1.TOP_L4_CEG_CODE,';
  V_SQL_TOP_L4_CEG_SHORT_CN_NAME         TEXT := 'T1.TOP_L4_CEG_SHORT_CN_NAME,';
  
  -- 量纲相关SQL查询字段
  V_SQL_DIMENSION_CODE                   TEXT := 'T1.DIMENSION_CODE,';
  V_SQL_DIMENSION_CN_NAME                TEXT := 'T1.DIMENSION_CN_NAME,';
  V_SQL_DIMENSION_SUBCATEGORY_CODE       TEXT := 'T1.DIMENSION_SUBCATEGORY_CODE,';
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME    TEXT := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
  V_SQL_DIMENSION_SUB_DETAIL_CODE        TEXT := 'T1.DIMENSION_SUB_DETAIL_CODE,';
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME     TEXT := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
  V_SQL_DMS_CODE                         TEXT := 'T1.DMS_CODE,';
  V_SQL_DMS_CN_NAME                      TEXT := 'T1.DMS_CN_NAME,';
  V_SQL_SPART_CODE                       TEXT := 'T1.SPART_CODE,';
  V_SQL_SPART_CN_NAME                    TEXT := 'T1.SPART_CN_NAME,';
  
  -- 动态变量（在循环中会被修改）
  V_GROUP_CODE                           TEXT;
  V_GROUP_NAME                           TEXT;
  V_CHILD_LEVEL                          TEXT;
  V_GROUP_LEVEL                          TEXT;
  V_SQL_PARENT_CODE                      TEXT;
  
  -- 关联条件
  V_JOIN_PROD_RND_TEAM_CODE              TEXT := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE';
  V_JOIN_DIMENSION_CODE                  TEXT := ' AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'') ';
  V_JOIN_DIMENSION_SUBCATEGORY_CODE      TEXT := ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'') ';
  V_JOIN_DIMENSION_SUB_DETAIL_CODE       TEXT := ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'') ';
  V_JOIN_DMS_CODE                        TEXT := ' AND NVL(T1.DMS_CODE,''DD'') = NVL(T2.DMS_CODE,''DD'') ';
  V_JOIN_SPART_CODE                      TEXT := ' AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') ';
  
BEGIN
  -- 初始化返回状态
  X_RESULT_STATUS := '1';
  
  -- 记录函数开始执行日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME || '开始执行 - ICT产业量纲颗粒度月度指数计算');
   
  -- 获取ICT产业最新版本号（固化逻辑：F_INDUSTRY_FLAG='I', F_ITEM_VERSION=NULL）
  V_STEP_NUM := V_STEP_NUM + 1;
  BEGIN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ITEM'
     ORDER BY LAST_UPDATE_DATE DESC 
     LIMIT 1;
     
    -- 记录版本号获取成功日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '成功获取ICT产业最新版本号：' || V_VERSION || '，基期：' || V_BASE_PERIOD_ID,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');
     
  EXCEPTION
    WHEN NO_DATA_FOUND THEN
      X_RESULT_STATUS := '0';
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '获取ICT产业版本号失败：未找到有效版本数据',
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => 'NO_DATA_FOUND');
      RETURN 'FAILED: 未找到有效版本数据';
    WHEN OTHERS THEN
      X_RESULT_STATUS := '0';
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '获取ICT产业版本号异常',
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => SQLSTATE || ':' || SQLERRM);
      RETURN 'FAILED: 获取版本号异常';
  END;

  -- ========== 开始12层循环卷积计算（量纲颗粒度固定逻辑）==========
  -- 循环说明：从最细粒度ITEM层级开始，逐层向上卷积聚合，最终到达LV0最高层级
  -- 卷积路径：ITEM -> CATEGORY -> MODL -> CEG -> DIMENSION -> SUBCATEGORY -> SUB_DETAIL -> SPART -> LV3 -> LV2 -> LV1 -> LV0
  FOR LEVEL_FLAG IN 1 .. V_LEVEL_NUM LOOP

    V_STEP_NUM := V_STEP_NUM + 1;

    -- 根据循环层级设置不同的字段和条件（每层有不同的业务逻辑）
    IF LEVEL_FLAG = 1 THEN
      -- 第1层：ITEM层级，跳过处理，保持初始设置
      -- 业务含义：最细粒度的产品项目数据，作为卷积计算的起点
      NULL;

    ELSIF LEVEL_FLAG = 2 THEN
      -- 第2层：CATEGORY -> MODL 卷积模块层级
      -- 业务含义：将产品类别数据按模块维度进行聚合，清空L4模块字段实现向上卷积
      V_TOP_L4_CEG_CODE              := '';  -- 清空L4模块编码字段，实现卷积
      V_TOP_L4_CEG_SHORT_CN_NAME     := '';  -- 清空L4模块名称字段
      V_SQL_TOP_L4_CEG_CODE          := '';  -- SQL中不再选择L4模块编码
      V_SQL_TOP_L4_CEG_SHORT_CN_NAME := '';  -- SQL中不再选择L4模块名称
      V_GROUP_CODE                   := 'TOP_L4_CEG_CODE AS PARENT_CODE,';           -- 以L4模块编码作为分组依据
      V_GROUP_NAME                   := 'TOP_L4_CEG_SHORT_CN_NAME AS PARENT_NAME,';  -- 以L4模块名称作为分组名称
      V_CHILD_LEVEL                  := '''CATEGORY''';  -- 子层级为CATEGORY
      V_GROUP_LEVEL                  := '''MODL''';      -- 当前层级为MODL（模块）
      V_SQL_PARENT_CODE              := 'T1.TOP_L3_CEG_CODE AS PARENT_CODE, T1.TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,'; -- 父层级为L3专家团

    ELSIF LEVEL_FLAG = 3 THEN
      -- 第3层：MODL -> CEG 卷积专家团层级
      -- 业务含义：将模块数据按专家团维度进行聚合，专家团是技术领域的核心组织单元
      V_TOP_L3_CEG_CODE              := '';  -- 清空L3专家团编码字段，实现卷积
      V_TOP_L3_CEG_SHORT_CN_NAME     := '';  -- 清空L3专家团名称字段
      V_SQL_TOP_L3_CEG_CODE          := '';  -- SQL中不再选择L3专家团编码
      V_SQL_TOP_L3_CEG_SHORT_CN_NAME := '';  -- SQL中不再选择L3专家团名称
      V_GROUP_CODE                   := 'TOP_L3_CEG_CODE AS PARENT_CODE,';           -- 以L3专家团编码作为分组依据
      V_GROUP_NAME                   := 'TOP_L3_CEG_SHORT_CN_NAME AS PARENT_NAME,';  -- 以L3专家团名称作为分组名称
      V_CHILD_LEVEL                  := '''MODL''';      -- 子层级为MODL（模块）
      V_GROUP_LEVEL                  := '''CEG''';       -- 当前层级为CEG（专家团）
      V_SQL_PARENT_CODE              := 'T1.DMS_CODE AS PARENT_CODE, T1.DMS_CN_NAME AS PARENT_CN_NAME,'; -- 父层级为量纲颗粒度

    ELSIF LEVEL_FLAG = 4 THEN
      -- 第4层：量纲颗粒度层级分视角处理
      V_GROUP_CODE      := 'DMS_CODE AS PARENT_CODE,';
      V_GROUP_NAME      := 'DMS_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL     := '''CEG''';
      V_GROUP_LEVEL     := '
       CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN ''DIMENSION''
            WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN ''SUBCATEGORY''
            WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN ''SUB_DETAIL''
            ELSE ''SPART''
        END ';
      V_SQL_PARENT_CODE := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CODE
           WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN T1.DIMENSION_SUBCATEGORY_CODE
           ELSE T1.DIMENSION_SUB_DETAIL_CODE
         END AS PARENT_CODE,
      CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CN_NAME
           WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CN_NAME
           WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
           ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
         END AS PARENT_CN_NAME, ';

    ELSIF LEVEL_FLAG = 5 THEN
      -- 第5层：量纲子类明细卷积（SPART -> SUB_DETAIL）
      V_SPART_CODE        := '';
      V_SPART_CN_NAME     := '';
      V_SQL_SPART_CODE    := '';
      V_SQL_SPART_CN_NAME := '';
      V_JOIN_SPART_CODE   := '';
      V_GROUP_CODE                       := 'DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE,';
      V_GROUP_NAME                       := 'DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                      := '''SPART''';
      V_GROUP_LEVEL                      := '''SUB_DETAIL''';
      V_SQL_PARENT_CODE                  := 'T1.DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_DMS_CODE                     := 'T1.DIMENSION_SUB_DETAIL_CODE,';
      V_SQL_DMS_CN_NAME                  := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';

    ELSIF LEVEL_FLAG = 6 THEN
      -- 第6层：量纲子类卷积（SUB_DETAIL -> SUBCATEGORY）
      V_DIMENSION_SUB_DETAIL_CODE        := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME     := '';
      V_SQL_DIMENSION_SUB_DETAIL_CODE    := '';
      V_SQL_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_JOIN_DIMENSION_SUB_DETAIL_CODE   := '';
      V_GROUP_CODE                       := 'DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,';
      V_GROUP_NAME                       := 'DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                      := '''SUB_DETAIL''';
      V_GROUP_LEVEL                      := '''SUBCATEGORY''';
      V_SQL_PARENT_CODE                  := 'T1.DIMENSION_CODE AS PARENT_CODE, T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_DMS_CODE                     := 'T1.DIMENSION_SUBCATEGORY_CODE,';
      V_SQL_DMS_CN_NAME                  := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';

    ELSIF LEVEL_FLAG = 7 THEN
      -- 第7层：量纲卷积（SUBCATEGORY -> DIMENSION）
      V_DIMENSION_SUBCATEGORY_CODE        := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME     := '';
      V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
      V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_JOIN_DIMENSION_SUBCATEGORY_CODE   := '';
      V_GROUP_CODE                        := 'DIMENSION_CODE AS PARENT_CODE,';
      V_GROUP_NAME                        := 'DIMENSION_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                       := '''SUBCATEGORY''';
      V_GROUP_LEVEL                       := '''DIMENSION''';
      V_SQL_PARENT_CODE                   := 'T1.PROD_RND_TEAM_CODE AS PARENT_CODE, T1.PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_DMS_CODE                      := 'T1.DIMENSION_CODE,';
      V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_CN_NAME,';

    ELSIF LEVEL_FLAG = 8 THEN
      -- 第8层：分视角卷积量纲父级-重量级团队
      V_DIMENSION_CODE               := '';
      V_DIMENSION_CN_NAME            := '';
      V_SQL_DIMENSION_CODE           := '';
      V_SQL_DIMENSION_CN_NAME        := '';
      V_JOIN_DIMENSION_CODE          := '';
      V_SQL_DMS_CODE                 := '';
      V_SQL_DMS_CN_NAME              := '';
      V_DMS_CODE                     := '';
      V_DMS_CN_NAME                  := '';
      V_JOIN_DMS_CODE                := '';
      V_LV3_PROD_RND_TEAM_CODE       := '';
      V_LV3_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';

      V_SQL_PARENT_CODE              := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RND_TEAM_CODE
           ELSE T1.LV2_PROD_RND_TEAM_CODE
      END AS PARENT_CODE,
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RD_TEAM_CN_NAME
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
           ELSE T1.LV2_PROD_RD_TEAM_CN_NAME
      END AS PARENT_CN_NAME, ';

      V_GROUP_LEVEL                  := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN ''LV1''
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
           ELSE ''LV3''
      END ';

      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'PARENT_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''DIMENSION''';

    ELSIF LEVEL_FLAG = 9 THEN
      -- 第9层：卷积LV3层级
      V_SQL_DMS_CODE                 := '';
      V_SQL_DMS_CN_NAME              := '';
      V_DMS_CODE                     := '';
      V_DMS_CN_NAME                  := '';
      V_JOIN_DMS_CODE                := '';
      V_LV3_PROD_RND_TEAM_CODE       := '';
      V_LV3_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';

      V_GROUP_CODE                   := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_SQL_PARENT_CODE              := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,';
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_GROUP_LEVEL                  := '''LV3''';
      V_CHILD_LEVEL                  := '''LV4''';

    ELSIF LEVEL_FLAG = 10 THEN
      -- 第10层：卷积LV2层级
      V_LV2_PROD_RND_TEAM_CODE       := '';
      V_LV2_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV2_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME := '';

      V_GROUP_CODE                   := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV3''';
      V_GROUP_LEVEL                  := '''LV2''';
      V_SQL_PARENT_CODE              := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,';
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';

    ELSIF LEVEL_FLAG = 11 THEN
      -- 第11层：卷积LV1层级
      V_LV1_PROD_RND_TEAM_CODE       := '';
      V_LV1_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV1_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME := '';

      V_GROUP_CODE                   := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV2''';
      V_GROUP_LEVEL                  := '''LV1''';
      V_SQL_PARENT_CODE              := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';

    ELSIF LEVEL_FLAG = 12 THEN
      -- 第12层：卷积LV0层级（最顶层）
      V_LV0_PROD_RND_TEAM_CODE       := '';
      V_LV0_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV0_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV0_PROD_RD_TEAM_CN_NAME := '';

      V_GROUP_CODE                   := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV1''';
      V_GROUP_LEVEL                  := '''LV0''';
      V_SQL_PARENT_CODE              := ''''' , '''', ';

    END IF;

    -- 构建并执行主要的卷积计算SQL
    V_SQL := '
    WITH BASE_INDEX AS
     (SELECT PERIOD_YEAR,
             PERIOD_ID,
             ' || V_PROD_RND_TEAM_CODE || V_PROD_RND_TEAM_CN_NAME ||
             V_DMS_CODE || V_DMS_CN_NAME ||
             V_LV0_PROD_RND_TEAM_CODE || V_LV0_PROD_RD_TEAM_CN_NAME ||
             V_LV1_PROD_RND_TEAM_CODE || V_LV1_PROD_RD_TEAM_CN_NAME ||
             V_LV2_PROD_RND_TEAM_CODE || V_LV2_PROD_RD_TEAM_CN_NAME ||
             V_LV3_PROD_RND_TEAM_CODE || V_LV3_PROD_RD_TEAM_CN_NAME ||
             V_DIMENSION_CODE || V_DIMENSION_CN_NAME ||
             V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_SPART_CODE || V_SPART_CN_NAME ||
             V_TOP_L3_CEG_CODE || V_TOP_L3_CEG_SHORT_CN_NAME ||
             V_TOP_L4_CEG_CODE || V_TOP_L4_CEG_SHORT_CN_NAME ||
             V_GROUP_CODE || V_GROUP_NAME || '
             GROUP_CODE,
             COST_INDEX,
             VIEW_FLAG,
             SCENARIO_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             GROUP_LEVEL
        FROM ' || V_MID_TABLE || '
       WHERE VERSION_ID = ' || V_VERSION || '
         AND UPPER(GROUP_LEVEL) = ' || V_CHILD_LEVEL || '
         AND BASE_PERIOD_ID = ' || V_BASE_PERIOD_ID || '),

    LEV_WEIGHT AS
     (SELECT PROD_RND_TEAM_CODE,
             ' || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE ||
             V_SPART_CODE || V_DMS_CODE || V_DMS_CN_NAME || '
             GROUP_CODE,
             WEIGHT_RATE,
             PARENT_CODE,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             GROUP_LEVEL
        FROM ' || V_WEIGHT_TABLE || '
       WHERE VERSION_ID = ' || V_VERSION || '
         AND UPPER(GROUP_LEVEL) = ' || V_CHILD_LEVEL || ' )

    INSERT INTO ' || V_MID_TABLE || '
      (VERSION_ID,
       BASE_PERIOD_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       ' || V_LV0_PROD_RND_TEAM_CODE || V_LV0_PROD_RD_TEAM_CN_NAME ||
       V_LV1_PROD_RND_TEAM_CODE || V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE || V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE || V_LV3_PROD_RD_TEAM_CN_NAME ||
       V_DIMENSION_CODE || V_DIMENSION_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_SPART_CODE || V_SPART_CN_NAME ||
       V_TOP_L3_CEG_CODE || V_TOP_L3_CEG_SHORT_CN_NAME ||
       V_TOP_L4_CEG_CODE || V_TOP_L4_CEG_SHORT_CN_NAME || '
       VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       ' || V_DMS_CODE || V_DMS_CN_NAME || '
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       COST_INDEX,
       PARENT_CODE,
       PARENT_CN_NAME,
       SCENARIO_FLAG,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME)
    SELECT ' || V_VERSION || ' AS VERSION_ID,
           ' || V_BASE_PERIOD_ID || ' AS BASE_PERIOD_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           ' || V_SQL_LV0_PROD_RND_TEAM_CODE || V_SQL_LV0_PROD_RD_TEAM_CN_NAME ||
           V_SQL_LV1_PROD_RND_TEAM_CODE || V_SQL_LV1_PROD_RD_TEAM_CN_NAME ||
           V_SQL_LV2_PROD_RND_TEAM_CODE || V_SQL_LV2_PROD_RD_TEAM_CN_NAME ||
           V_SQL_LV3_PROD_RND_TEAM_CODE || V_SQL_LV3_PROD_RD_TEAM_CN_NAME ||
           V_SQL_DIMENSION_CODE || V_SQL_DIMENSION_CN_NAME ||
           V_SQL_DIMENSION_SUBCATEGORY_CODE || V_SQL_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_SQL_DIMENSION_SUB_DETAIL_CODE || V_SQL_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_SQL_SPART_CODE || V_SQL_SPART_CN_NAME ||
           V_SQL_TOP_L3_CEG_CODE || V_SQL_TOP_L3_CEG_SHORT_CN_NAME ||
           V_SQL_TOP_L4_CEG_CODE || V_SQL_TOP_L4_CEG_SHORT_CN_NAME || '
           T1.VIEW_FLAG,
           ' || V_SQL_PROD_RND_TEAM_CODE || V_SQL_PROD_RND_TEAM_CN_NAME ||
           V_SQL_DMS_CODE || V_SQL_DMS_CN_NAME || '
           T1.PARENT_CODE AS GROUP_CODE,
           T1.PARENT_NAME AS GROUP_CN_NAME,
           ' || V_GROUP_LEVEL || ' AS GROUP_LEVEL,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           ' || V_SQL_PARENT_CODE || '
           T1.SCENARIO_FLAG,
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME
      FROM BASE_INDEX T1
      JOIN LEV_WEIGHT T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       ' || V_JOIN_PROD_RND_TEAM_CODE || V_JOIN_DMS_CODE || V_JOIN_DIMENSION_CODE ||
       V_JOIN_DIMENSION_SUBCATEGORY_CODE || V_JOIN_DIMENSION_SUB_DETAIL_CODE || V_JOIN_SPART_CODE || '
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
     GROUP BY ' || V_SQL_LV0_PROD_RND_TEAM_CODE || V_SQL_LV0_PROD_RD_TEAM_CN_NAME ||
       V_SQL_LV1_PROD_RND_TEAM_CODE || V_SQL_LV1_PROD_RD_TEAM_CN_NAME ||
       V_SQL_LV2_PROD_RND_TEAM_CODE || V_SQL_LV2_PROD_RD_TEAM_CN_NAME ||
       V_SQL_LV3_PROD_RND_TEAM_CODE || V_SQL_LV3_PROD_RD_TEAM_CN_NAME ||
       V_SQL_DIMENSION_CODE || V_SQL_DIMENSION_CN_NAME ||
       V_SQL_DIMENSION_SUBCATEGORY_CODE || V_SQL_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_SQL_DIMENSION_SUB_DETAIL_CODE || V_SQL_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_SQL_SPART_CODE || V_SQL_SPART_CN_NAME ||
       V_SQL_TOP_L3_CEG_CODE || V_SQL_TOP_L3_CEG_SHORT_CN_NAME ||
       V_SQL_TOP_L4_CEG_CODE || V_SQL_TOP_L4_CEG_SHORT_CN_NAME ||
       V_SQL_PROD_RND_TEAM_CODE || V_SQL_PROD_RND_TEAM_CN_NAME ||
       V_SQL_DMS_CODE || V_SQL_DMS_CN_NAME || '
       T1.SCENARIO_FLAG,
       T1.PERIOD_YEAR,
       T1.PERIOD_ID,
       T1.PARENT_NAME,
       T1.PARENT_CODE,
       T1.VIEW_FLAG,
       T1.GROUP_LEVEL,
       T1.CALIBER_FLAG,
       T1.OVERSEA_FLAG,
       T1.LV0_PROD_LIST_CODE,
       T1.LV0_PROD_LIST_CN_NAME;';

    -- 执行卷积计算SQL
    BEGIN
      EXECUTE IMMEDIATE V_SQL;

      -- 记录成功执行日志
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '第' || LEVEL_FLAG || '次循环 ' || V_GROUP_LEVEL || ' 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT,
       F_FORMULA_SQL_TXT => V_SQL,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_DML_ROW_COUNT => SQL%ROWCOUNT,
       F_ERRBUF => 'SUCCESS');

    EXCEPTION
      WHEN OTHERS THEN
        X_RESULT_STATUS := '0';
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => '第' || LEVEL_FLAG || '次循环执行失败',
         F_FORMULA_SQL_TXT => V_SQL,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => SQLSTATE || ':' || SQLERRM);
        RETURN 'FAILED: 第' || LEVEL_FLAG || '次循环执行异常';
    END;

  END LOOP; -- 结束12层循环

  -- 删除目标表中同版本的旧数据
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL := 'DELETE FROM ' || V_TARGET_TABLE || ' WHERE VERSION_ID = ' || V_VERSION || ';';

  BEGIN
    EXECUTE IMMEDIATE V_SQL;

    -- 记录删除操作日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '指数表同版本数据删除完成，删除行数：' || SQL%ROWCOUNT,
     F_FORMULA_SQL_TXT => V_SQL,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_ERRBUF => 'SUCCESS');

  EXCEPTION
    WHEN OTHERS THEN
      X_RESULT_STATUS := '0';
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '删除指数表同版本数据失败',
       F_FORMULA_SQL_TXT => V_SQL,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => SQLSTATE || ':' || SQLERRM);
      RETURN 'FAILED: 删除旧数据异常';
  END;

  -- 将中间表数据插入到目标表
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL := '
  INSERT INTO ' || V_TARGET_TABLE || '
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     ' || V_PROD_RND_TEAM_CODE || V_PROD_RND_TEAM_CN_NAME ||
     V_DIMENSION_CODE || V_DIMENSION_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_SPART_CODE || V_SPART_CN_NAME ||
     V_DMS_CODE || V_DMS_CN_NAME || '
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
  SELECT ' || V_VERSION || ' AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         BASE_PERIOD_ID,
         ' || V_PROD_RND_TEAM_CODE || V_PROD_RND_TEAM_CN_NAME ||
         V_DIMENSION_CODE || V_DIMENSION_CN_NAME ||
         V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUBCATEGORY_CN_NAME ||
         V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUB_DETAIL_CN_NAME ||
         V_SPART_CODE || V_SPART_CN_NAME ||
         V_DMS_CODE || V_DMS_CN_NAME || '
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         COST_INDEX,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         VIEW_FLAG,
         APPEND_FLAG,
         SCENARIO_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM ' || V_MID_TABLE || ';';

  BEGIN
    EXECUTE IMMEDIATE V_SQL;

    -- 记录插入操作成功日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '指数表插数完成，插入行数：' || SQL%ROWCOUNT,
     F_FORMULA_SQL_TXT => V_SQL,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_ERRBUF => 'SUCCESS');

    -- 记录函数执行完成日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => V_SP_NAME || '执行完成 - ICT产业量纲颗粒度月度指数计算成功',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');

  EXCEPTION
    WHEN OTHERS THEN
      X_RESULT_STATUS := '0';
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '指数表插数失败',
       F_FORMULA_SQL_TXT => V_SQL,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => SQLSTATE || ':' || SQLERRM);
      RETURN 'FAILED: 插入数据异常';
  END;

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
    X_RESULT_STATUS := '0';

    -- 记录全局异常日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => V_SP_NAME || '第' || V_STEP_NUM || '步运行失败',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => SQLSTATE || ':' || SQLERRM);

    RETURN 'FAILED: ' || SQLSTATE || ':' || SQLERRM;

END;

$$
/
